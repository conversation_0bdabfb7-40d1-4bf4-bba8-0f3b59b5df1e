import { Button } from '@heroui/react';
import { XIcon } from 'lucide-react';

interface BookThisPlanProps {
  onClose?: () => void;
}

const BookThisPlan = ({ onClose }: BookThisPlanProps) => {
  return (
    <div className="bg-white h-full w-full flex flex-col">
      {/* Header */}
      <div className="flex items-center justify-between p-6 border-b border-gray-200">
        <h2 className="text-2xl font-semibold text-gray-900">Book This Plan</h2>
        {onClose && (
          <Button
            isIconOnly
            variant="light"
            onPress={onClose}
            className="text-gray-500 hover:text-gray-700"
          >
            <XIcon size={24} />
          </Button>
        )}
      </div>

      {/* Content */}
      <div className="flex-1 p-6 overflow-y-auto">
        <div className="max-w-4xl mx-auto">
          <div className="text-center py-20">
            <div className="mb-8">
              <div className="w-24 h-24 bg-gradient-to-r from-purple-600 to-indigo-600 rounded-full mx-auto mb-6 flex items-center justify-center">
                <span className="text-white text-3xl font-bold">✈</span>
              </div>
              <h3 className="text-3xl font-bold text-gray-900 mb-4">
                Ready to Book Your Adventure?
              </h3>
              <p className="text-lg text-gray-600 mb-8 max-w-2xl mx-auto">
                You're just one step away from an amazing journey! Our booking
                system will help you secure your perfect travel plan.
              </p>
            </div>

            <div className="space-y-4">
              <Button
                size="lg"
                className="bg-gradient-to-r from-purple-600 to-indigo-600 text-white font-semibold px-8 py-3 rounded-full shadow-lg hover:opacity-90 transition-opacity"
              >
                Start Booking Process
              </Button>
              <p className="text-sm text-gray-500">
                Secure booking • Best price guarantee • 24/7 support
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BookThisPlan;
