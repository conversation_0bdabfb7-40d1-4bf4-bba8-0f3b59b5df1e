'use client';

import { useState } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  Modal<PERSON>eader,
  ModalBody,
  ModalFooter,
} from '@heroui/modal';
import { Checkbox } from '@heroui/checkbox';
import { Button } from '@heroui/button';
import { Select, SelectItem } from '@heroui/select';
import { Slider } from '@heroui/slider';
import { Chip } from '@heroui/chip';

const amenitiesList = [
  'Air conditioning',
  'Assisted living',
  'Disability Access',
  'Controlled access',
  'Cable Ready',
  'Available now',
  'College',
  'Corporate',
  'Elevator',
  'Extra Storage',
  'High speed internet',
  'Garage',
  'Pet allowed',
];

export default function FlightFilter({
  open,
  onClose,
  onApply,
}: {
  open: boolean;
  onClose: () => void;
  onApply: (filters: any) => void;
}) {
  const [sortBy, setSortBy] = useState<string[]>([]);
  const [location, setLocation] = useState('');
  const [price, setPrice] = useState<[number, number]>([0, 10000000000]);
  const [amenities, setAmenities] = useState<string[]>([]);

  const handleApply = () => {
    onApply({ sortBy, location, price, amenities });
    onClose();
  };

  return (
    <Modal isOpen={open} onClose={onClose} size="lg" scrollBehavior="inside">
      <ModalContent>
        {() => (
          <>
            <ModalHeader className="text-default-1000">Filter</ModalHeader>
            <ModalBody className="space-y-4">
              {/* Location */}
              <div>
                <p className="font-semibold mb-2 text-default-Secondary">
                  Departure airport
                </p>
                <Select
                  placeholder="Any area"
                  selectedKeys={[location]}
                  onChange={e => setLocation(e.target.value)}
                  variant="bordered"
                >
                  <SelectItem key="">Any area</SelectItem>
                  <SelectItem key="New York">New York</SelectItem>
                  <SelectItem key="Los Angeles">Los Angeles</SelectItem>
                </Select>
              </div>

              {/* Location */}
              <div>
                <p className="font-semibold mb-2 text-default-Secondary">
                  Arrival airport
                </p>
                <Select
                  placeholder="Any area"
                  selectedKeys={[location]}
                  onChange={e => setLocation(e.target.value)}
                  variant="bordered"
                >
                  <SelectItem key="">Any area</SelectItem>
                  <SelectItem key="New York">New York</SelectItem>
                  <SelectItem key="Los Angeles">Los Angeles</SelectItem>
                </Select>
              </div>

              <div className="grid grid-cols-2">
                {/* Sort By */}
                <div>
                  <p className="font-semibold mb-2 text-default-Secondary">
                    Sort by
                  </p>
                  <div className="flex flex-col gap-2">
                    {[
                      'Early departure',
                      'Late Deparure',
                      'early arrival',
                      'Late arrival',
                    ].map(label => (
                      <Checkbox
                        key={label}
                        isSelected={sortBy.includes(label)}
                        onChange={e =>
                          setSortBy(prev =>
                            e.target.checked
                              ? [...prev, label]
                              : prev.filter(s => s !== label)
                          )
                        }
                        className="text-default-1000"
                      >
                        {label}
                      </Checkbox>
                    ))}
                  </div>
                </div>{' '}
                {/* Stops */}
                <div>
                  <p className="font-semibold mb-2 text-default-Secondary">
                    Stops
                  </p>
                  <div className="flex flex-col gap-2">
                    {['One way', 'Round trip', 'Multi-city'].map(label => (
                      <Checkbox
                        key={label}
                        isSelected={sortBy.includes(label)}
                        onChange={e =>
                          setSortBy(prev =>
                            e.target.checked
                              ? [...prev, label]
                              : prev.filter(s => s !== label)
                          )
                        }
                        className="text-default-1000"
                      >
                        {label}
                      </Checkbox>
                    ))}
                  </div>
                </div>
              </div>

              {/* Price Range */}
              <div>
                <p className="font-semibold mb-2 text-default-Secondary">
                  Price range
                </p>
                <Slider
                  label=""
                  minValue={0}
                  maxValue={10000000000}
                  step={1000}
                  value={price}
                  onChange={val => setPrice(val as [number, number])}
                  formatOptions={{ style: 'currency', currency: 'USD' }}
                />
                <div className="flex justify-between text-sm text-gray-500">
                  <span>${price[0].toLocaleString()}</span>
                  <span>${price[1].toLocaleString()}</span>
                </div>
              </div>

              {/* Airlines */}
              <div>
                <div>
                  <p className="font-semibold mb-2 text-default-Secondary">
                    Airlines
                  </p>
                  <div className="flex flex-wrap gap-2">
                    {[
                      'British Airways',
                      'Lufthansa',
                      'Ryanair',
                      'easyJet',
                      'Air France',
                      'KLM',
                      'Wizz Air',
                    ].map(label => (
                      <Checkbox
                        key={label}
                        isSelected={sortBy.includes(label)}
                        onChange={e =>
                          setSortBy(prev =>
                            e.target.checked
                              ? [...prev, label]
                              : prev.filter(s => s !== label)
                          )
                        }
                        className="text-default-1000"
                      >
                        {label}
                      </Checkbox>
                    ))}
                  </div>
                </div>
              </div>
            </ModalBody>
            <ModalFooter className="flex justify-between">
              <Button
                variant="light"
                onPress={() => {
                  setSortBy([]);
                  setLocation('');
                  setPrice([0, 10000000000]);
                  setAmenities([]);
                }}
                color="primary"
                className="font-semibold"
              >
                Reset all
              </Button>
              <Button
                onPress={handleApply}
                color="primary"
                className="rounded-full font-semibold"
              >
                Apply
              </Button>
            </ModalFooter>
          </>
        )}
      </ModalContent>
    </Modal>
  );
}
