import React, { useState } from 'react';
import { Accordion, AccordionItem } from '@heroui/accordion';
import { Button } from '@heroui/react';
import { FaPlane } from 'react-icons/fa';
import { ArrowIcon, EmptyCircleIcon, FillCircleIcon } from '@/components/icons';

interface FlightCardProps {
  id?: string;
  airlineLogo: string;
  airlineName: string;
  airlineType: string;
  departureTime: string;
  arrivalTime: string;
  departureDate: string;
  arrivalDate: string;
  departureCity: string;
  arrivalCity: string;
  departureAirport: string;
  arrivalAirport: string;
  duration: string;
  aircraft: string;
  price: string;
  onClick?: () => void;
}

const DepartingFlight: React.FC<FlightCardProps> = ({
  id = 'flight-1',
  airlineLogo,
  airlineName,
  airlineType,
  departureTime,
  arrivalTime,
  departureDate,
  arrivalDate,
  departureCity,
  arrivalCity,
  departureAirport,
  arrivalAirport,
  duration,
  aircraft,
  price,
  onClick,
}) => {
  // ✅ keep as Set<string> (valid for Accordion)
  const [selectedKeys, setSelectedKeys] = useState<Set<string>>(new Set());

  const handleSelectionChange = (keys: any) => {
    if (keys === 'all') {
      setSelectedKeys(new Set(['all']));
    } else if (keys instanceof Set) {
      setSelectedKeys(keys as Set<string>);
    }
  };

  const isOpen = selectedKeys.has(id);

  return (
    <div
      className={
        isOpen
          ? 'w-full rounded-2xl border border-[#EFEFFF] overflow-hidden'
          : 'w-full rounded-2xl border border-cardborder overflow-hidden'
      }
    >
      <Accordion
        selectionMode="single"
        selectionBehavior="toggle"
        selectedKeys={selectedKeys}
        onSelectionChange={handleSelectionChange}
        variant="shadow"
        className="rounded-tr-2xl rounded-none p-0
        "
      >
        <AccordionItem
          key={id}
          value={id}
          classNames={{
            base: 'rounded-tr-2xl rounded-none',
            trigger:
              'px-4 py-3 border-b border-gray-300 transition-colors data-[open=true]:bg-[#EFEFFF] data-[open=false]:bg-white',
            content: 'bg-[#EFEFFF]',
          }}
          title={
            <div className="grid grid-cols-3 items-center w-full px-4">
              {/* left */}
              <div className="flex items-center gap-2">
                <div className="flex flex-col gap-3 justify-center items-center md:justify-start border border-cardborder aspect-square w-[60px] h-[60px] rounded-lg p-3">
                  <img
                    src="https://storage.googleapis.com/nxvoy-logos/operator_logos/IGO.png"
                    alt={airlineName}
                    className="h-[60px] object-contain"
                  />
                </div>
                <span className="font-medium">{airlineType}</span>
              </div>

              {/* center */}
              <div className="flex items-center gap-1 justify-center">
                <div className="text-left">
                  <p className="text-lg font-semibold">{departureTime}</p>
                  <p className="text-xs text-gray-500">{departureDate}</p>
                  <p className="text-xs text-gray-500">{departureCity}</p>
                </div>

                <div className="w-[120px] flex flex-col items-center text-default-700">
                  <div className="relative w-full h-6 flex items-center justify-center">
                    <div className="w-full border-t border-default-400 rotate-90 md:rotate-0" />
                    <FaPlane
                      className="z-10 text-[#525252] rotate-90 md:rotate-0 absolute left-1/2 -translate-x-1/2"
                      size={24}
                    />
                  </div>
                  <p className="mt-1 text-xs text-[#525252]">Non stop</p>
                  <p className="text-xs text-[#525252]">{duration}</p>
                </div>
                <div className="text-left">
                  <p className="text-lg font-semibold">{arrivalTime}</p>
                  <p className="text-xs text-gray-500">{arrivalDate}</p>
                  <p className="text-xs text-gray-500">{arrivalCity}</p>
                </div>
              </div>

              {/* right */}
              <div />
            </div>
          }
        >
          <div className="px-6 py-4 ">
            <div className="flex flex-row items-centers pl-4 pb-4">
              <div>
                <div className="space-y-[6px] pt-1 flex flex-col jusctify-between items-center">
                  <EmptyCircleIcon isAnimation={false} />
                  <ArrowIcon isAnimation={false} />
                  <FillCircleIcon isAnimation={false} />
                </div>
              </div>
              <div className="px-6 space-y-2 text-sm">
                <p className="font-semibold text-subtitle">
                  {departureTime} – {departureAirport} ({departureCity})
                </p>
                <p className="text-gray-500">Travel time: {duration}</p>
                <p className="font-semibold text-subtitle">
                  {arrivalTime} – {arrivalAirport} ({arrivalCity})
                </p>
              </div>
            </div>
            <p className="text-gray-500">
              {airlineName}, Economy, {aircraft}
            </p>
          </div>
        </AccordionItem>
      </Accordion>

      {/* footer always visible */}
      {isOpen && <div className="border-t border-gray-300" />}
      <div
        className={`transition-colors ${isOpen ? 'bg-[#EFEFFF]' : 'bg-white'}`}
      >
        <div className="flex items-center justify-between px-6 py-4">
          <div>
            <span className="text-lg font-bold text-subtitle">{price}</span>

            <p className="text-sm text-subtitle">Per person</p>
          </div>
          <Button
            color="primary"
            className="rounded-full font-medium"
            onPress={onClick}
          >
            Select Flight
          </Button>
        </div>
      </div>
    </div>
  );
};

export default DepartingFlight;
