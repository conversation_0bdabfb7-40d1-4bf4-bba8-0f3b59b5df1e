# MediaLightbox Components

A collection of reusable React components for displaying media galleries with lightbox functionality.

## Components

### MediaLightbox

A full-featured lightbox component for displaying images and videos with thumbnail navigation.

#### Features
- Support for both images and videos
- Thumbnail navigation with customizable position and styling
- Blurred background with glassmorphism effect (user preference)
- Smooth animations and transitions
- Fully configurable through props
- Accessibility support
- Mobile responsive

#### Props

```typescript
interface MediaLightboxProps {
  open: boolean;                    // Whether the lightbox is open
  onClose: () => void;             // Function to close the lightbox
  slides: MediaSlide[];            // Array of media slides to display
  index: number;                   // Current slide index
  onSlideChange?: (index: number) => void; // Function called when slide changes
  
  // Configuration objects for customization
  thumbnailConfig?: ThumbnailConfig;
  videoConfig?: VideoConfig;
  carouselConfig?: CarouselConfig;
  animationConfig?: AnimationConfig;
  controllerConfig?: ControllerConfig;
  customStyles?: CustomStyles;
}
```

#### Basic Usage

```tsx
import { MediaLightbox, createSlidesFromUrls } from '@/components/globalComponents';

const [lightboxOpen, setLightboxOpen] = useState(false);
const [lightboxIndex, setLightboxIndex] = useState(0);

const imageUrls = ['image1.jpg', 'image2.jpg', 'video.mp4'];
const slides = createSlidesFromUrls(imageUrls);

<MediaLightbox
  open={lightboxOpen}
  onClose={() => setLightboxOpen(false)}
  slides={slides}
  index={lightboxIndex}
  onSlideChange={setLightboxIndex}
/>
```

### MediaThumbnail

A component for rendering media thumbnails with automatic video detection and play button overlay.

#### Features
- Automatic detection of video vs image content
- Video thumbnails with customizable play button overlay
- Support for Next.js Image optimization
- Configurable play button styling
- Responsive design
- Accessibility support

#### Props

```typescript
interface MediaThumbnailProps {
  src: string;                     // Source URL of the media
  alt: string;                     // Alt text for accessibility
  width: number;                   // Width of the thumbnail
  height: number;                  // Height of the thumbnail
  className?: string;              // CSS classes to apply
  isVideo?: boolean;               // Whether to force video treatment
  playButtonConfig?: PlayButtonConfig; // Custom play button configuration
  videoProps?: VideoProps;         // Video-specific props
}
```

#### Basic Usage

```tsx
import { MediaThumbnail } from '@/components/globalComponents';

<MediaThumbnail
  src="https://example.com/image.jpg"
  alt="Example Image"
  width={200}
  height={150}
  className="rounded-lg hover:opacity-80"
/>

// For videos, play button is automatically added
<MediaThumbnail
  src="https://example.com/video.mp4"
  alt="Example Video"
  width={200}
  height={150}
  className="rounded-lg hover:opacity-80"
/>
```

## Utility Functions

### createSlidesFromUrls
Creates slides array from a simple array of URLs.

```tsx
const slides = createSlidesFromUrls(['img1.jpg', 'img2.jpg'], 'Gallery Image');
```

### createSlidesFromRecommendation
Creates slides from recommendation data structure (large + small images).

```tsx
const media = {
  large: 'main-image.jpg',
  small: ['thumb1.jpg', 'thumb2.jpg', 'video.mp4']
};
const slides = createSlidesFromRecommendation(media);
```

### createSlidesFromUrlsWithAlts
Creates slides with custom alt texts and types.

```tsx
const items = [
  { url: 'image.jpg', alt: 'Beautiful landscape', type: 'image' },
  { url: 'video.mp4', alt: 'Travel video', type: 'video' }
];
const slides = createSlidesFromUrlsWithAlts(items);
```

## Configuration Options

### Thumbnail Configuration
```typescript
thumbnailConfig={{
  position: 'bottom',        // 'top' | 'bottom' | 'start' | 'end'
  width: 100,               // Thumbnail width
  height: 70,               // Thumbnail height
  border: 2,                // Border width
  borderRadius: 12,         // Border radius
  padding: 6,               // Container padding
  gap: 16,                  // Gap between thumbnails
  showToggle: false,        // Show/hide toggle button
}}
```

### Video Configuration
```typescript
videoConfig={{
  controls: true,           // Show video controls
  playsInline: true,        // Play inline on mobile
  preload: 'metadata',      // 'none' | 'metadata' | 'auto'
}}
```

### Custom Styles
```typescript
customStyles={{
  container: {
    backgroundColor: 'rgba(255, 255, 255, 0.15)',
    backdropFilter: 'blur(20px) saturate(180%)',
  },
  thumbnailsContainer: {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    backdropFilter: 'blur(15px) saturate(150%)',
  },
}}
```

## Migration from ExploreRecommendation

The components were extracted from the ExploreRecommendation card. To migrate existing code:

1. Replace direct lightbox imports with global components:
```tsx
// Before
import Lightbox from 'yet-another-react-lightbox';
import './lightbox-custom.css';

// After
import { MediaLightbox, MediaThumbnail, createSlidesFromRecommendation } from '@/components/globalComponents';
```

2. Replace inline MediaThumbnail component with global one
3. Use utility functions to create slides
4. Update lightbox props to match new interface

## Files Structure

```
src/components/globalComponents/
├── MediaLightbox.tsx           # Main lightbox component
├── MediaThumbnail.tsx          # Thumbnail component
├── MediaLightboxUtils.tsx      # Utility functions
├── lightbox-custom.css         # Custom lightbox styles
├── MediaLightboxExample.tsx    # Usage examples
├── MediaLightbox.README.md     # This documentation
└── index.ts                    # Exports
```
