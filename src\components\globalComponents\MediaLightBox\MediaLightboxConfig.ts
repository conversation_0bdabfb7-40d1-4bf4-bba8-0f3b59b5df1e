/**
 * Global configuration for MediaLightbox component
 *
 * This file contains default configurations that can be reused across
 * different instances of MediaLightbox to maintain consistency and
 * reduce code duplication.
 */

export interface MediaLightboxGlobalConfig {
  thumbnailConfig: {
    position: 'top' | 'bottom' | 'start' | 'end';
    width: number;
    height: number;
    border: number;
    borderRadius: number;
    padding: number;
    gap: number;
    showToggle: boolean;
  };
  videoConfig: {
    controls: boolean;
    playsInline: boolean;
    preload: 'none' | 'metadata' | 'auto';
  };
  carouselConfig: {
    finite: boolean;
    preload: number;
    spacing: `${number}px` | `${number}%` | number;
  };
  animationConfig: {
    fade: number;
    swipe: number;
    easing: {
      fade: string;
      swipe: string;
    };
  };
  controllerConfig: {
    closeOnBackdropClick: boolean;
    closeOnPullDown: boolean;
    closeOnPullUp: boolean;
  };
  customStyles: {
    container: React.CSSProperties;
    thumbnailsContainer: React.CSSProperties;
  };
}

/**
 * Default configuration for MediaLightbox
 *
 * This configuration provides a consistent, optimized setup for the lightbox
 * with blurred backgrounds, smooth animations, and proper video support.
 */
export const DEFAULT_MEDIA_LIGHTBOX_CONFIG: MediaLightboxGlobalConfig = {
  thumbnailConfig: {
    position: 'bottom',
    width: 100,
    height: 70,
    border: 2,
    borderRadius: 12,
    padding: 6,
    gap: 16,
    showToggle: false,
  },
  videoConfig: {
    controls: true,
    playsInline: true,
    preload: 'metadata',
  },
  carouselConfig: {
    finite: false,
    preload: 2,
    spacing: '20px' as const,
  },
  animationConfig: {
    fade: 400,
    swipe: 600,
    easing: {
      fade: 'cubic-bezier(0.4, 0, 0.2, 1)',
      swipe: 'cubic-bezier(0.4, 0, 0.2, 1)',
    },
  },
  controllerConfig: {
    closeOnBackdropClick: true,
    closeOnPullDown: true,
    closeOnPullUp: true,
  },
  customStyles: {
    container: {
      backgroundColor: 'rgba(255, 255, 255, 0.15)',
      backdropFilter: 'blur(20px) saturate(180%)',
    },
    thumbnailsContainer: {
      backgroundColor: 'rgba(255, 255, 255, 0.1)',
      backdropFilter: 'blur(15px) saturate(150%)',
    },
  },
};

/**
 * Compact configuration for smaller lightbox instances
 *
 * Useful for smaller thumbnails or mobile views
 */
export const COMPACT_MEDIA_LIGHTBOX_CONFIG: Partial<MediaLightboxGlobalConfig> =
  {
    thumbnailConfig: {
      position: 'bottom',
      width: 80,
      height: 60,
      border: 2,
      borderRadius: 8,
      padding: 4,
      gap: 12,
      showToggle: false,
    },
    customStyles: {
      container: {
        backgroundColor: 'rgba(255, 255, 255, 0.12)',
        backdropFilter: 'blur(15px) saturate(160%)',
      },
      thumbnailsContainer: {
        backgroundColor: 'rgba(255, 255, 255, 0.08)',
        backdropFilter: 'blur(12px) saturate(140%)',
      },
    },
  };

/**
 * Gallery-focused configuration for image-heavy content
 *
 * Optimized for showcasing multiple images with enhanced thumbnails
 */
export const GALLERY_MEDIA_LIGHTBOX_CONFIG: Partial<MediaLightboxGlobalConfig> =
  {
    thumbnailConfig: {
      position: 'bottom',
      width: 120,
      height: 80,
      border: 3,
      borderRadius: 16,
      padding: 8,
      gap: 20,
      showToggle: true,
    },
    carouselConfig: {
      finite: false,
      preload: 3,
      spacing: '24px' as const,
    },
    animationConfig: {
      fade: 300,
      swipe: 500,
      easing: {
        fade: 'cubic-bezier(0.25, 0.46, 0.45, 0.94)',
        swipe: 'cubic-bezier(0.25, 0.46, 0.45, 0.94)',
      },
    },
  };

/**
 * Utility function to merge configurations
 *
 * @param baseConfig - Base configuration to start with
 * @param overrides - Configuration overrides to apply
 * @returns Merged configuration
 */
export function mergeMediaLightboxConfig(
  baseConfig: MediaLightboxGlobalConfig,
  overrides: Partial<MediaLightboxGlobalConfig>
): MediaLightboxGlobalConfig {
  return {
    thumbnailConfig: {
      ...baseConfig.thumbnailConfig,
      ...overrides.thumbnailConfig,
    },
    videoConfig: { ...baseConfig.videoConfig, ...overrides.videoConfig },
    carouselConfig: {
      ...baseConfig.carouselConfig,
      ...overrides.carouselConfig,
    },
    animationConfig: {
      ...baseConfig.animationConfig,
      ...overrides.animationConfig,
      easing: {
        ...baseConfig.animationConfig.easing,
        ...overrides.animationConfig?.easing,
      },
    },
    controllerConfig: {
      ...baseConfig.controllerConfig,
      ...overrides.controllerConfig,
    },
    customStyles: {
      container: {
        ...baseConfig.customStyles.container,
        ...overrides.customStyles?.container,
      },
      thumbnailsContainer: {
        ...baseConfig.customStyles.thumbnailsContainer,
        ...overrides.customStyles?.thumbnailsContainer,
      },
    },
  };
}

/**
 * Hook for using MediaLightbox configurations
 *
 * @param configType - Type of configuration to use
 * @param overrides - Additional overrides to apply
 * @returns Complete configuration object
 */
export function useMediaLightboxConfig(
  configType: 'default' | 'compact' | 'gallery' = 'default',
  overrides: Partial<MediaLightboxGlobalConfig> = {}
): MediaLightboxGlobalConfig {
  let baseConfig = DEFAULT_MEDIA_LIGHTBOX_CONFIG;

  if (configType === 'compact') {
    baseConfig = mergeMediaLightboxConfig(
      baseConfig,
      COMPACT_MEDIA_LIGHTBOX_CONFIG
    );
  } else if (configType === 'gallery') {
    baseConfig = mergeMediaLightboxConfig(
      baseConfig,
      GALLERY_MEDIA_LIGHTBOX_CONFIG
    );
  }

  return mergeMediaLightboxConfig(baseConfig, overrides);
}
