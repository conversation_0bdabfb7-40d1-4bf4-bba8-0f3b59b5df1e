import Image from 'next/image';

export interface MediaThumbnailProps {
  /** Source URL of the media */
  src: string;
  /** Alt text for accessibility */
  alt: string;
  /** Width of the thumbnail */
  width: number;
  /** Height of the thumbnail */
  height: number;
  /** CSS classes to apply */
  className?: string;
  /** Whether to force video treatment */
  isVideo?: boolean;
  /** Custom play button configuration */
  playButtonConfig?: {
    size?: 'small' | 'medium' | 'large';
    customSize?: string;
    iconSize?: string;
    backgroundColor?: string;
    iconColor?: string;
  };
  /** Video-specific props */
  videoProps?: {
    preload?: 'none' | 'metadata' | 'auto';
    muted?: boolean;
    playsInline?: boolean;
  };
}

/**
 * MediaThumbnail - A reusable component for rendering media thumbnails
 *
 * Features:
 * - Automatic detection of video vs image content
 * - Video thumbnails with play button overlay
 * - Configurable play button styling
 * - Support for Next.js Image optimization
 * - Responsive design
 * - Accessibility support
 */
const MediaThumbnail = ({
  src,
  alt,
  width,
  height,
  className = '',
  isVideo,
  playButtonConfig = {},
  videoProps = {},
}: MediaThumbnailProps) => {
  // Helper function to detect if a URL is a video
  const isVideoUrl = (url: string): boolean => {
    const videoExtensions = [
      '.mp4',
      '.webm',
      '.ogg',
      '.mov',
      '.avi',
      '.mkv',
      '.m4v',
    ];
    const videoHosts = [
      'gtv-videos-bucket',
      'youtube.com',
      'vimeo.com',
      'sample-videos.com',
    ];

    return (
      videoExtensions.some(ext =>
        url.toLowerCase().includes(ext.toLowerCase())
      ) || videoHosts.some(host => url.toLowerCase().includes(host))
    );
  };

  const isVideoContent = isVideo || isVideoUrl(src);

  // Play button configuration with defaults
  const getPlayButtonSize = () => {
    if (playButtonConfig.customSize) {
      return playButtonConfig.customSize;
    }

    const isSmallThumbnail = width <= 53;

    switch (playButtonConfig.size) {
      case 'small':
        return 'w-4 h-4';
      case 'large':
        return 'w-12 h-12';
      case 'medium':
      default:
        return isSmallThumbnail ? 'w-4 h-4' : 'w-8 h-8';
    }
  };

  const getPlayIconSize = () => {
    if (playButtonConfig.iconSize) {
      return playButtonConfig.iconSize;
    }

    const isSmallThumbnail = width <= 53;

    switch (playButtonConfig.size) {
      case 'small':
        return 'text-xs';
      case 'large':
        return 'text-lg';
      case 'medium':
      default:
        return isSmallThumbnail ? 'text-xs' : 'text-sm';
    }
  };

  const playButtonSize = getPlayButtonSize();
  const playIconSize = getPlayIconSize();
  const backgroundColor =
    playButtonConfig.backgroundColor || 'bg-white bg-opacity-80';
  const iconColor = playButtonConfig.iconColor || 'text-black';

  // Default video props
  const defaultVideoProps = {
    preload: 'metadata' as const,
    muted: true,
    playsInline: true,
    ...videoProps,
  };

  if (isVideoContent) {
    return (
      <div
        className={`${className} bg-gray-100 flex items-center justify-center relative overflow-hidden`}
        style={{ width, height }}
      >
        <video
          src={src}
          className="w-full h-full object-cover"
          {...defaultVideoProps}
          onLoadedData={e => {
            // Seek to a frame that's likely to have content (1 second in)
            const video = e.target as HTMLVideoElement;
            if (video.duration > 1) {
              video.currentTime = 1;
            }
          }}
          onLoadedMetadata={e => {
            // Also try to seek when metadata is loaded
            const video = e.target as HTMLVideoElement;
            if (video.duration > 1) {
              video.currentTime = Math.min(1, video.duration * 0.1); // 10% into the video or 1 second
            }
          }}
          onError={e => {
            // Handle video loading errors by showing a fallback
            const video = e.target as HTMLVideoElement;
            video.style.display = 'none';
            const parent = video.parentElement;
            if (parent && !parent.querySelector('.video-fallback')) {
              parent.style.background =
                'linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%)';
              const fallback = document.createElement('div');
              fallback.className = 'video-fallback';
              fallback.innerHTML = '🎥';
              fallback.style.fontSize = '24px';
              fallback.style.color = 'rgba(0, 0, 0, 0.5)';
              fallback.style.position = 'absolute';
              fallback.style.top = '50%';
              fallback.style.left = '50%';
              fallback.style.transform = 'translate(-50%, -50%)';
              fallback.style.zIndex = '0';
              parent.appendChild(fallback);
            }
          }}
          poster="" // Prevent default poster from showing
          crossOrigin="anonymous" // Help with CORS issues
        >
          <track kind="captions" srcLang="en" label="English" default />
        </video>
        <div className="absolute inset-0 bg-black bg-opacity-30 flex items-center justify-center">
          <div
            className={`${playButtonSize} ${backgroundColor} rounded-full flex items-center justify-center shadow-lg`}
          >
            <span className={`${iconColor} ${playIconSize}`}>▶</span>
          </div>
        </div>
      </div>
    );
  }

  return (
    <Image
      src={src}
      alt={alt}
      width={width}
      height={height}
      className={className}
    />
  );
};

export default MediaThumbnail;
