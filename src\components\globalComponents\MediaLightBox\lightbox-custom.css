/* Custom styles for the enhanced lightbox gallery */

/* Light blurred background instead of dark */
.yarl__container {
  background-color: rgba(255, 255, 255, 0.15) !important;
  backdrop-filter: blur(20px) saturate(180%) !important;
  -webkit-backdrop-filter: blur(20px) saturate(180%) !important;
  /* Enable hardware acceleration for smoother animations */
  transform: translateZ(0) !important;
  will-change: transform, opacity !important;
}

/* Integrated thumbnail styling - seamless with main interface */
.yarl__thumbnails_container {
  background: rgba(255, 255, 255, 0.1) !important;
  backdrop-filter: blur(15px) saturate(150%) !important;
  -webkit-backdrop-filter: blur(15px) saturate(150%) !important;
  border-radius: 16px !important;
  padding: 20px !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1) !important;
}

/* Individual thumbnail styling - better integration */
.yarl__thumbnails_thumbnail {
  border: 2px solid rgba(255, 255, 255, 0.4) !important;
  border-radius: 12px !important;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1) !important;
  cursor: pointer !important;
  overflow: hidden !important;
  background: rgba(255, 255, 255, 0.1) !important;
  backdrop-filter: blur(10px) !important;
  -webkit-backdrop-filter: blur(10px) !important;
  transform: translateZ(0) !important; /* Enable hardware acceleration */
}

/* Thumbnail hover effect - more subtle and integrated */
.yarl__thumbnails_thumbnail:hover {
  border-color: rgba(255, 255, 255, 0.7) !important;
  transform: scale(1.05) translateY(-2px) translateZ(0) !important;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15) !important;
  background: rgba(255, 255, 255, 0.2) !important;
}

/* Active thumbnail styling - more prominent but integrated */
.yarl__thumbnails_thumbnail--active {
  border-color: #707FF5 !important;
  box-shadow: 0 0 0 3px rgba(112, 127, 245, 0.4), 0 8px 25px rgba(112, 127, 245, 0.2) !important;
  transform: scale(1.08) translateY(-3px) translateZ(0) !important;
  background: rgba(112, 127, 245, 0.1) !important;
}

/* Thumbnail image styling */
.yarl__thumbnails_thumbnail img,
.yarl__thumbnails_thumbnail video {
  object-fit: cover !important;
  width: 100% !important;
  height: 100% !important;
  transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1) !important;
  transform: translateZ(0) !important; /* Hardware acceleration */
}

/* Thumbnail image hover effect */
.yarl__thumbnails_thumbnail:hover img,
.yarl__thumbnails_thumbnail:hover video {
  transform: scale(1.1) translateZ(0) !important;
}

/* Scrollable thumbnails track - better integration */
.yarl__thumbnails_track {
  scrollbar-width: thin !important;
  scrollbar-color: rgba(112, 127, 245, 0.5) rgba(255, 255, 255, 0.1) !important;
  padding: 8px 0 !important;
}

/* Webkit scrollbar styling for thumbnails - more polished */
.yarl__thumbnails_track::-webkit-scrollbar {
  height: 8px !important;
}

.yarl__thumbnails_track::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1) !important;
  border-radius: 4px !important;
  margin: 0 8px !important;
}

.yarl__thumbnails_track::-webkit-scrollbar-thumb {
  background: linear-gradient(90deg, #707FF5, #8B9BFF) !important;
  border-radius: 4px !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
}

.yarl__thumbnails_track::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(90deg, #5A6FE8, #7A8AFF) !important;
  box-shadow: 0 2px 8px rgba(112, 127, 245, 0.3) !important;
}

/* Video thumbnail overlay - better visibility and integration */
.yarl__thumbnails_thumbnail[data-video="true"]::after {
  content: "▶";
  position: absolute !important;
  top: 50% !important;
  left: 50% !important;
  transform: translate(-50%, -50%) !important;
  color: white !important;
  font-size: 18px !important;
  text-shadow: 0 2px 8px rgba(0, 0, 0, 0.6) !important;
  pointer-events: none !important;
  background: rgba(0, 0, 0, 0.4) !important;
  border-radius: 50% !important;
  width: 32px !important;
  height: 32px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  backdrop-filter: blur(4px) !important;
  -webkit-backdrop-filter: blur(4px) !important;
}

/* Video thumbnail hover effect */
.yarl__thumbnails_thumbnail[data-video="true"]:hover::after {
  background: rgba(112, 127, 245, 0.8) !important;
  transform: translate(-50%, -50%) scale(1.1) !important;
}

/* Smooth transitions for all elements */
.yarl__thumbnails_container * {
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

/* Enhanced slide transitions */
.yarl__slide {
  transition: transform 0.6s cubic-bezier(0.4, 0, 0.2, 1), opacity 0.4s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

/* Smooth thumbnail track scrolling */
.yarl__thumbnails_track {
  scroll-behavior: smooth !important;
}

/* Enhanced vignette effect - lighter and more subtle */
.yarl__thumbnails_vignette {
  background: linear-gradient(
    90deg,
    rgba(255, 255, 255, 0.3) 0%,
    transparent 20%,
    transparent 80%,
    rgba(255, 255, 255, 0.3) 100%
  ) !important;
}

/* Main slide area styling - better integration */
.yarl__slide {
  backdrop-filter: blur(1px) !important;
  -webkit-backdrop-filter: blur(1px) !important;
}

/* Unified background for main content area */
.yarl__slide_container {
  background: rgba(255, 255, 255, 0.05) !important;
  backdrop-filter: blur(10px) !important;
  -webkit-backdrop-filter: blur(10px) !important;
  border-radius: 12px !important;
  margin: 20px !important;
  border: 1px solid rgba(255, 255, 255, 0.1) !important;
}

/* Video player styling for consistency */
.yarl__slide video {
  border-radius: 8px !important;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1) !important;
  background: rgba(0, 0, 0, 0.05) !important;
}

/* Navigation buttons styling */
.yarl__button {
  background: rgba(255, 255, 255, 0.15) !important;
  backdrop-filter: blur(10px) !important;
  -webkit-backdrop-filter: blur(10px) !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
  color: rgba(255, 255, 255, 0.9) !important;
}

.yarl__button:hover {
  background: rgba(255, 255, 255, 0.25) !important;
  border-color: rgba(255, 255, 255, 0.4) !important;
  color: white !important;
}

/* Mobile responsive adjustments */
@media (max-width: 768px) {
  .yarl__thumbnails_container {
    margin: 12px !important;
    padding: 16px !important;
    border-radius: 12px !important;
  }

  .yarl__thumbnails_thumbnail {
    border-width: 1px !important;
    border-radius: 8px !important;
  }

  .yarl__container {
    background-color: rgba(255, 255, 255, 0.1) !important;
  }
}

/* Tablet adjustments */
@media (max-width: 1024px) and (min-width: 769px) {
  .yarl__thumbnails_container {
    padding: 18px !important;
  }
}

/* Large screen optimizations */
@media (min-width: 1440px) {
  .yarl__thumbnails_container {
    padding: 24px !important;
  }
}

/* Loading state for thumbnails */
.yarl__thumbnails_thumbnail img[src=""] {
  background: linear-gradient(90deg, rgba(255, 255, 255, 0.1) 25%, rgba(255, 255, 255, 0.2) 50%, rgba(255, 255, 255, 0.1) 75%) !important;
  background-size: 200% 100% !important;
  animation: loading 1.5s infinite !important;
}

@keyframes loading {
  0% {
    background-position: 200% 0 !important;
  }
  100% {
    background-position: -200% 0 !important;
  }
}

/* Video thumbnail specific styling */
.yarl__thumbnails_thumbnail video {
  object-fit: cover !important;
  width: 100% !important;
  height: 100% !important;
  pointer-events: none !important;
}

/* Enhanced video thumbnail detection and styling */
.yarl__thumbnails_thumbnail[data-yarl-video="true"] {
  position: relative !important;
}

.yarl__thumbnails_thumbnail[data-yarl-video="true"]::before {
  content: "▶" !important;
  position: absolute !important;
  top: 50% !important;
  left: 50% !important;
  transform: translate(-50%, -50%) !important;
  color: white !important;
  font-size: 16px !important;
  text-shadow: 0 2px 8px rgba(0, 0, 0, 0.8) !important;
  pointer-events: none !important;
  background: rgba(0, 0, 0, 0.6) !important;
  border-radius: 50% !important;
  width: 28px !important;
  height: 28px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  backdrop-filter: blur(4px) !important;
  -webkit-backdrop-filter: blur(4px) !important;
  z-index: 10 !important;
  transition: all 0.3s ease !important;
}

.yarl__thumbnails_thumbnail[data-yarl-video="true"]:hover::before {
  background: rgba(112, 127, 245, 0.9) !important;
  transform: translate(-50%, -50%) scale(1.1) !important;
}

/* Enhanced focus states for accessibility */
.yarl__thumbnails_thumbnail:focus {
  outline: 2px solid #707FF5 !important;
  outline-offset: 2px !important;
  border-color: #707FF5 !important;
}

/* Smooth entrance animation for thumbnails */
.yarl__thumbnails_thumbnail {
  animation: thumbnailFadeIn 0.3s ease-out !important;
}

@keyframes thumbnailFadeIn {
  from {
    opacity: 0 !important;
    transform: translateY(10px) !important;
  }
  to {
    opacity: 1 !important;
    transform: translateY(0) !important;
  }
}

/* Better integration with main slide area */
.yarl__slide img,
.yarl__slide video {
  border-radius: 8px !important;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1) !important;
}

/* Close button styling */
.yarl__button[aria-label*="Close"] {
  background: rgba(255, 255, 255, 0.2) !important;
  backdrop-filter: blur(10px) !important;
  -webkit-backdrop-filter: blur(10px) !important;
  border: 1px solid rgba(255, 255, 255, 0.3) !important;
  border-radius: 50% !important;
}

.yarl__button[aria-label*="Close"]:hover {
  background: rgba(255, 255, 255, 0.3) !important;
  border-color: rgba(255, 255, 255, 0.5) !important;
}

/* Recommendation card layout fixes */
.recommendation-card-container {
  overflow: hidden !important;
  max-width: 100% !important;
}

.recommendation-image-grid {
  max-width: 230px !important;
  min-width: 0 !important;
  flex-shrink: 0 !important;
}

.recommendation-small-grid {
  width: 100% !important;
  max-width: 230px !important;
  grid-template-columns: repeat(4, 53px) !important;
  gap: 6px !important;
}

.recommendation-small-thumbnail {
  width: 53px !important;
  height: 53px !important;
  flex-shrink: 0 !important;
  overflow: hidden !important;
}

/* Performance optimizations for smooth animations */
.yarl__thumbnails_container,
.yarl__thumbnails_thumbnail,
.yarl__slide {
  will-change: transform, opacity !important;
  backface-visibility: hidden !important;
  -webkit-backface-visibility: hidden !important;
}

/* Ensure video controls are properly styled */
.yarl__slide video::-webkit-media-controls-panel {
  background: rgba(0, 0, 0, 0.8) !important;
  backdrop-filter: blur(10px) !important;
  -webkit-backdrop-filter: blur(10px) !important;
}

/* Enhanced focus states for better accessibility */
.yarl__slide video:focus {
  outline: 2px solid #707FF5 !important;
  outline-offset: 4px !important;
}

/* Video plugin styles (since no separate CSS file exists) */
.yarl__slide video {
  max-width: 100% !important;
  max-height: 100% !important;
  width: auto !important;
  height: auto !important;
  object-fit: contain !important;
}

/* Video controls styling */
.yarl__slide video::-webkit-media-controls {
  background: rgba(0, 0, 0, 0.8) !important;
  border-radius: 8px !important;
}

.yarl__slide video::-webkit-media-controls-play-button {
  background-color: rgba(112, 127, 245, 0.9) !important;
  border-radius: 50% !important;
}

.yarl__slide video::-webkit-media-controls-timeline {
  background: rgba(255, 255, 255, 0.2) !important;
  border-radius: 4px !important;
}

.yarl__slide video::-webkit-media-controls-current-time-display,
.yarl__slide video::-webkit-media-controls-time-remaining-display {
  color: white !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5) !important;
}

/* Video loading state */
.yarl__slide video[data-loading="true"] {
  background: linear-gradient(90deg, rgba(255, 255, 255, 0.1) 25%, rgba(255, 255, 255, 0.2) 50%, rgba(255, 255, 255, 0.1) 75%) !important;
  background-size: 200% 100% !important;
  animation: loading 1.5s infinite !important;
}
