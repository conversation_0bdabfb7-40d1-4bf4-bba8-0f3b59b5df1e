'use client';

import Image from 'next/image';
import { TbLayoutSidebarLeftCollapse } from 'react-icons/tb';
import { useState } from 'react';

import LoginModal from '@/components/dashboard/LoginModal';
import {
  BellIcon,
  ChatIcon,
  SearchIcon,
  SettingIcon,
} from '@/components/icons';

export default function Header({
  isLogin,
  onSidebarToggle,
}: {
  onSidebarToggle: () => void;
  isLogin?: boolean;
}) {
  const [openLoginModal, setLoginModal] = useState(false);
  return (
    <header className="w-full flex justify-between items-center px-6 py-3 max-md:px-3 bg-white z-20 relative">
      {/* Sidebar Toggle - Mobile Only */}
      <div className="md:hidden mr-3 flex flex-row gap-3">
        <Image
          src="https://storage.googleapis.com/nxvoytrips-img/nxvoy-travel-agent-holidayPlanner/logo.svg"
          alt="Logo"
          width={36}
          height={36}
        />
        <button
          type="button"
          className="sidebar-toggle-btn flex z-50 bg-white rounded-md"
          onClick={onSidebarToggle}
        >
          <TbLayoutSidebarLeftCollapse size={32} strokeWidth={1.5} />
        </button>
      </div>

      {/* Search Input - Hidden on Mobile */}
      <div className="flex items-center bg-gray-100 rounded-full pl-4 pr-1 h-12 w-full max-w-md transition-all focus-within:ring-2 focus-within:ring-violet-400 max-md:hidden">
        <input
          type="text"
          placeholder="Searching..."
          className="flex-1 bg-transparent outline-none text-sm text-gray-700 placeholder-gray-500"
        />
        <button
          type="button"
          className="h-10 w-10 rounded-full bg-primary-200 text-white flex items-center justify-center hover:bg-violet-600 transition"
        >
          <SearchIcon isAnimation={false} />
        </button>
      </div>

      {/* Right Section */}
      <div className="flex items-center max-md:justify-end max-md:items-end gap-4 ">
        {isLogin && (
          <button
            type="button"
            className="h-10 w-10 rounded-full bg-[#E8E8E8] flex items-center justify-center  hover:bg-gray-100 transition max-md:hidden"
          >
            <BellIcon size={18} isAnimation={false} />
          </button>
        )}
        {isLogin && (
          <button
            type="button"
            className="h-10 w-10 rounded-full bg-[#E8E8E8] flex items-center justify-center  hover:bg-gray-100 transition max-md:hidden"
          >
            <ChatIcon isAnimation={false} />
          </button>
        )}
        <button
          type="button"
          className="h-10 w-10 rounded-full bg-[#E8E8E8] flex items-center justify-center  hover:bg-gray-100 transition max-md:hidden"
        >
          <SettingIcon isAnimation={false} />
        </button>

        {!isLogin && (
          <button
            type="button"
            onClick={() => setLoginModal(!openLoginModal)}
            className="px-5 py-2 cursor-pointer rounded-full btn-gradient text-white text-sm font-semibold shadow-md hover:opacity-90 transition"
          >
            Login/ Signup
          </button>
        )}
      </div>
      <LoginModal open={openLoginModal} />
    </header>
  );
}
