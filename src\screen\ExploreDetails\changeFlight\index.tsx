const ChangeFlight = ({ onBack }: { onBack?: () => void }) => {
  return (
    <div className="bg-white rounded-xl p-4">
      <div className="flex items-center justify-between mb-4">
        <h2 className="text-xl font-semibold">Change Flight</h2>
        {onBack && (
          <button
            type="button"
            onClick={onBack}
            className="px-4 py-2 text-sm text-primary-200 hover:underline"
          >
            ← Back to Itinerary
          </button>
        )}
      </div>
      <div className="text-gray-600">
        <p>Flight change functionality will be implemented here.</p>
      </div>
    </div>
  );
};

export default ChangeFlight;
