import PhotoExpenseCard from './PhotoExpenseCard';
import TabsItinerary from './tabs';

const TabsSection = ({
  location,
  title,
  onChangeFlightClick,
  onAddActivityClick,
}: {
  location: string;
  title: string;
  onChangeFlightClick?: () => void;
  onAddActivityClick?: () => void;
}) => {
  return (
    <div>
      <div>
        <PhotoExpenseCard location={location} title={title} />
      </div>
      <div className="bg-white rounded-xl p-3">
        <TabsItinerary
          onChangeFlightClick={onChangeFlightClick}
          onAddActivityClick={onAddActivityClick}
        />
      </div>
    </div>
  );
};

export default TabsSection;
