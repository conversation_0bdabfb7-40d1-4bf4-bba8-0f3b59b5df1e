import PhotoExpenseCard from './PhotoExpenseCard';
import TabsItinerary from './tabs';

const TabsSection = ({
  location,
  title,
  onChangeFlightClick,
}: {
  location: string;
  title: string;
  onChangeFlightClick?: () => void;
}) => {
  return (
    <div>
      <div>
        <PhotoExpenseCard location={location} title={title} />
      </div>
      <div className="bg-white rounded-xl p-3">
        <TabsItinerary onChangeFlightClick={onChangeFlightClick} />
      </div>
    </div>
  );
};

export default TabsSection;
