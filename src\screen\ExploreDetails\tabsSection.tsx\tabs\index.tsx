'use client';

import { Tabs, Tab } from '@heroui/react';
import { useState } from 'react';

import Itinerary from './Itinerary';
import Transport from './Transport';
import Hotel from './Hotel';
import Activities from './Activities';
import Calendar from './Calendar';

export default function TabsItinerary({
  onChangeFlightClick,
  onAddActivityClick,
}: {
  onChangeFlightClick?: () => void;
  onAddActivityClick?: () => void;
}) {
  const [selectedTab, setSelectedTab] = useState('itinerary');

  return (
    <div className="flex w-full flex-col">
      <Tabs
        aria-label="Options"
        selectedKey={selectedTab}
        onSelectionChange={key => setSelectedTab(String(key))}
        classNames={{
          tabList: 'w-full flex flex-row justify-between gap-6',
          tab: `
            relative h-12 px-1 max-w-fit  flex items-end
            group-data-[selected=true]:gradient-border
          `,
          cursor: 'w-full bg-gradient-to-r from-[#ec0fac] to-[#6b30ea]',
          tabContent: `
            pb-1 text-lg text-gray-600 text-center border-red-300
            group-data-[selected=true]:text-black
            group-data-[selected=true]:font-bold
            font-medium
          `,
        }}
        variant="underlined"
      >
        <Tab key="itinerary" title="Itinerary" />
        <Tab key="transport" title="Transport" />
        <Tab key="hotel" title="Hotel" />
        <Tab key="activities" title="Activities" />
        <Tab key="calendar" title="Calendar" />
      </Tabs>

      <div className="mt-4 text-sm text-gray-700">
        {selectedTab === 'itinerary' && (
          <Itinerary
            onChangeFlightClick={onChangeFlightClick}
            onAddActivityClick={onAddActivityClick}
          />
        )}
        {selectedTab === 'transport' && (
          <Transport onChangeFlightClick={onChangeFlightClick} />
        )}
        {selectedTab === 'hotel' && <Hotel />}
        {selectedTab === 'activities' && (
          <Activities onAddActivityClick={onAddActivityClick} />
        )}
        {selectedTab === 'calendar' && <Calendar />}
      </div>
    </div>
  );
}
