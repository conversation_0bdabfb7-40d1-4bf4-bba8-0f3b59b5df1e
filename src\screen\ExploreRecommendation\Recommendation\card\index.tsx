import { MapPointIcon, CalendarIcon } from '@/components/icons';
import { Card, CardBody, Button } from '@heroui/react';
import { useState } from 'react';
import {
  MediaLightbox,
  MediaThumbnail,
  createSlidesFromRecommendation,
} from '@/components/globalComponents/MediaLightBox';

import Link from 'next/link';

interface Recommendation {
  title: string;
  duration: string;
  location: string;
  tags: string;
  media: {
    large: string;
    small: string[];
  };
  badge: string;
}

interface RecommendationCardProps {
  recommendations: Recommendation;
}
/**
 * Enhanced RecommendationCard with Gallery Features
 *
 * Features:
 * - Bottom thumbnail gallery with clickable navigation
 * - Support for both images and videos
 * - Blurred background instead of solid black
 * - Scrollable thumbnails for many images
 * - Active thumbnail highlighting
 * - Direct navigation by clicking on grid images
 * - Smooth animations and transitions
 */
const RecommendationCard = ({ recommendations }: RecommendationCardProps) => {
  const [lightboxOpen, setLightboxOpen] = useState(false);
  const [lightboxIndex, setLightboxIndex] = useState(0);

  // Create slides array for lightbox from recommendation media
  const slides = createSlidesFromRecommendation(recommendations.media);

  // Handle opening lightbox from a specific image in the grid
  const handleImageClick = (e: React.MouseEvent, index: number) => {
    e.preventDefault();
    e.stopPropagation();
    setLightboxIndex(index);
    setLightboxOpen(true);
  };

  return (
    <div className="flex flex-col gap-2 flex-1 overflow-hidden mb-3">
      <Card
        key={`${recommendations.title}-${recommendations.location}-${recommendations.duration}`}
        className="bg-white border-none shadow-none hover:border-black hover:!bg-white transition-all duration-300 ease-in-out flex-shrink-0"
        isHoverable
        isPressable
        data-card="recommendation" // Used for DOM measurements
      >
        <CardBody>
          <div className="flex flex-row max-md:flex-col justify-between rounded-xl cursor-pointer w-full overflow-hidden recommendation-card-container">
            {/* Left section: Image and details */}
            <div className="flex flex-row max-md:flex-col gap-4 min-w-0 flex-1">
              {/* grid Image  */}

              <div className="flex flex-col gap-1.5 w-full max-w-[230px] min-w-0 recommendation-image-grid">
                {/* Large Image */}
                <div
                  className="w-full cursor-pointer flex-shrink-0"
                  onClick={e => handleImageClick(e, 0)}
                  onKeyDown={e => {
                    if (e.key === 'Enter' || e.key === ' ') {
                      e.preventDefault();
                      e.stopPropagation();
                      setLightboxIndex(0);
                      setLightboxOpen(true);
                    }
                  }}
                  role="button"
                  tabIndex={0}
                  aria-label="View large image in gallery"
                >
                  <MediaThumbnail
                    src={recommendations.media.large}
                    alt="Large Image"
                    width={230}
                    height={120}
                    className="w-full h-[120px] object-cover rounded-tl-xl rounded-tr-xl hover:opacity-90 transition-opacity"
                  />
                </div>

                {/* Small Image Grid - Fixed 53x53 size */}
                <div className="grid grid-cols-4 gap-1.5 w-full recommendation-small-grid">
                  {recommendations.media.small.slice(0, 3).map((src, index) => (
                    <div
                      key={index}
                      className="cursor-pointer w-[53px] h-[53px] flex-shrink-0 recommendation-small-thumbnail"
                      onClick={e => handleImageClick(e, index + 1)}
                      onKeyDown={e => {
                        if (e.key === 'Enter' || e.key === ' ') {
                          e.preventDefault();
                          e.stopPropagation();
                          setLightboxIndex(index + 1);
                          setLightboxOpen(true);
                        }
                      }}
                      role="button"
                      tabIndex={0}
                      aria-label={`View small image ${index + 1} in gallery`}
                    >
                      <MediaThumbnail
                        src={src}
                        alt={`Small Image ${index + 1}`}
                        width={53}
                        height={53}
                        className="w-[53px] h-[53px] object-cover hover:opacity-90 transition-opacity rounded-sm"
                      />
                    </div>
                  ))}

                  {/* Blurred Last Image with Overlay Text - Fixed 53x53 size */}
                  <div
                    className="relative cursor-pointer w-[53px] h-[53px] flex-shrink-0 recommendation-small-thumbnail"
                    onClick={e => {
                      e.preventDefault();
                      e.stopPropagation();
                      // Start from the 4th image (index 4) if it exists, otherwise start from beginning
                      const startIndex =
                        recommendations.media.small.length > 3 ? 4 : 0;
                      setLightboxIndex(startIndex);
                      setLightboxOpen(true);
                    }}
                    onKeyDown={e => {
                      if (e.key === 'Enter' || e.key === ' ') {
                        e.preventDefault();
                        e.stopPropagation();
                        const startIndex =
                          recommendations.media.small.length > 3 ? 4 : 0;
                        setLightboxIndex(startIndex);
                        setLightboxOpen(true);
                      }
                    }}
                    role="button"
                    tabIndex={0}
                    aria-label={`View all ${slides.length} images in gallery`}
                  >
                    <MediaThumbnail
                      src={recommendations.media.small[3]}
                      alt="Small Image 4 (Blurred)"
                      width={53}
                      height={53}
                      className="w-[53px] h-[53px] object-cover rounded-sm blur-xs"
                    />
                    <div className="absolute inset-0 flex items-center justify-center hover:bg-black/20 transition-colors rounded-sm">
                      <span className="text-white text-xs font-medium leading-tight text-center">
                        View All
                        <br />({slides.length})
                      </span>
                    </div>
                  </div>
                </div>
              </div>
              {/* Text content */}
              <div className="text-sm space-y-0.5 min-w-0 flex-1">
                <p className="font-medium text-lg">{recommendations.title}</p>
                <div className="flex flex-row gap-3 py-2">
                  <div className="flex flex-row gap-1 items-center">
                    <CalendarIcon
                      size={14}
                      isAnimation={false}
                      className="text-black"
                    />
                    <p className=" text-md">{recommendations.duration}</p>
                  </div>

                  <div className="flex flex-row gap-1 items-center">
                    <MapPointIcon
                      size={14}
                      isAnimation={false}
                      className="text-black"
                    />
                    <p className=" text-md">{recommendations.location}</p>
                  </div>
                </div>
                <div>
                  <p className="text-default-700 text-md">
                    Book with only 30% of total payment
                  </p>
                  <p className="text-default-700 text-md">
                    Customize free of cost
                  </p>
                </div>

                <p className="text-md font-medium py-2">
                  Activities, Explore, Leisure, Family
                </p>

                <p className="text-md text-default-700">
                  Explore Berlin's historic neighborhoods and vibrant street
                  art.
                </p>
              </div>
            </div>
            {/* Right section: Action button */}
            <div className="flex flex-col gap-2 justify-between text-right">
              <p className="text-base font-bold">
                4.2{' '}
                <span className="text-default-700 font-medium">
                  (103 Ratings)
                </span>
              </p>
              <div className="text-right">
                <div className="">
                  <span className="text-default-700 line-through text-md">
                    $3,500
                  </span>
                  <span className="font-bold ml-3 text-xl">$2,999</span>
                </div>
                <div className="flex justify-end py-2">
                  <p className="text-default-700 text-md w-[130px] text-right">
                    $150 taxes & fees Per Person
                  </p>
                </div>
                <Link
                  href={`/explore/${recommendations.location
                    .toLowerCase()
                    .replace(/\s/g, '-')}/${recommendations.title
                    .toLowerCase()
                    .replace(/\s/g, '-')}`}
                  // className="px-5 py-2 rounded-full bg-primary text-white text-sm font-semibold hover:opacity-90 transition text-center"
                >
                  <Button
                    color="primary"
                    // variant="bordered"
                    size="sm"
                    className="font-semibold  rounded-full bg-primary-200 text-white"
                  >
                    Book Trip
                  </Button>
                </Link>
              </div>
            </div>
          </div>
        </CardBody>
      </Card>

      {/* Enhanced Lightbox Component with Gallery Features */}
      <MediaLightbox
        open={lightboxOpen}
        onClose={() => setLightboxOpen(false)}
        slides={slides}
        index={lightboxIndex}
        onSlideChange={(index: number) => setLightboxIndex(index)}
        configType="default"
      />
    </div>
  );
};

export default RecommendationCard;
